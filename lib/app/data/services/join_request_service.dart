import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../models/join_request_model.dart';
import '../models/project_model.dart';
import 'firebase_service.dart';
import 'auth_service.dart';
import 'project_service.dart';

/// Production-ready Join Request Service for Firebase operations
class JoinRequestService extends GetxService {
  static JoinRequestService get instance => Get.find();

  final FirebaseService _firebaseService = FirebaseService.instance;
  final AuthService _authService = AuthService.instance;
  final ProjectService _projectService = ProjectService.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Collection reference
  static const String _joinRequestsCollection = 'joinRequests';

  /// Send a join request to a project
  Future<JoinRequestModel> sendJoinRequest({
    required String projectId,
    required String message,
  }) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      // Check if project exists and is public
      final project = await _projectService.getProject(projectId);
      if (project == null) {
        throw 'Project not found';
      }

      // Check if user is already a member
      if (project.isMember(currentUser.uid)) {
        throw 'You are already a member of this project';
      }

      // Check if there's already a pending request
      final existingRequest = await _getPendingRequest(projectId, currentUser.uid);
      if (existingRequest != null) {
        throw 'You already have a pending request for this project';
      }

      final now = DateTime.now();
      final joinRequest = JoinRequestModel(
        id: '', // Will be set by Firestore
        projectId: projectId,
        projectName: project.name,
        userId: currentUser.uid,
        userName: currentUser.name,
        userEmail: currentUser.email,
        message: message.trim(),
        status: JoinRequestStatus.pending,
        requestedAt: now,
      );

      // Add to Firestore
      final docRef = await _firestore
          .collection(_joinRequestsCollection)
          .add(joinRequest.toFirestore());

      // Return request with the generated ID
      final createdRequest = joinRequest.copyWith(id: docRef.id);

      if (Get.isLogEnable) {
        Get.log('Join request sent successfully: ${createdRequest.id}');
      }

      return createdRequest;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error sending join request: $e');
      }
      rethrow;
    }
  }

  /// Get pending join requests for a project (for project owners/managers)
  Future<List<JoinRequestModel>> getProjectJoinRequests(String projectId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_joinRequestsCollection)
          .where('projectId', isEqualTo: projectId)
          .where('status', isEqualTo: 'pending')
          .get();

      final requests = querySnapshot.docs
          .map((doc) => JoinRequestModel.fromFirestore(doc))
          .toList();

      // Sort by request date (newest first)
      requests.sort((a, b) => b.requestedAt.compareTo(a.requestedAt));

      if (Get.isLogEnable) {
        Get.log('Fetched ${requests.length} join requests for project: $projectId');
      }

      return requests;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching project join requests: $e');
      }
      rethrow;
    }
  }

  /// Get user's join requests
  Future<List<JoinRequestModel>> getUserJoinRequests() async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      final querySnapshot = await _firestore
          .collection(_joinRequestsCollection)
          .where('userId', isEqualTo: currentUser.uid)
          .get();

      final requests = querySnapshot.docs
          .map((doc) => JoinRequestModel.fromFirestore(doc))
          .toList();

      // Sort by request date (newest first)
      requests.sort((a, b) => b.requestedAt.compareTo(a.requestedAt));

      if (Get.isLogEnable) {
        Get.log('Fetched ${requests.length} join requests for user: ${currentUser.uid}');
      }

      return requests;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching user join requests: $e');
      }
      rethrow;
    }
  }

  /// Approve a join request
  Future<void> approveJoinRequest(String requestId) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      // Get the join request
      final requestDoc = await _firestore
          .collection(_joinRequestsCollection)
          .doc(requestId)
          .get();

      if (!requestDoc.exists) {
        throw 'Join request not found';
      }

      final joinRequest = JoinRequestModel.fromFirestore(requestDoc);

      // Check if current user is project owner/manager
      final project = await _projectService.getProject(joinRequest.projectId);
      if (project == null) {
        throw 'Project not found';
      }

      if (!project.isManager(currentUser.uid)) {
        throw 'You do not have permission to approve join requests for this project';
      }

      // Update join request status
      await _firestore
          .collection(_joinRequestsCollection)
          .doc(requestId)
          .update({
        'status': JoinRequestStatus.approved.name,
        'respondedAt': Timestamp.fromDate(DateTime.now()),
        'respondedBy': currentUser.uid,
      });

      // Add user to project members
      await _projectService.addMemberToProject(joinRequest.projectId, joinRequest.userId);

      if (Get.isLogEnable) {
        Get.log('Join request approved: $requestId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error approving join request: $e');
      }
      rethrow;
    }
  }

  /// Reject a join request
  Future<void> rejectJoinRequest(String requestId) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      // Get the join request
      final requestDoc = await _firestore
          .collection(_joinRequestsCollection)
          .doc(requestId)
          .get();

      if (!requestDoc.exists) {
        throw 'Join request not found';
      }

      final joinRequest = JoinRequestModel.fromFirestore(requestDoc);

      // Check if current user is project owner/manager
      final project = await _projectService.getProject(joinRequest.projectId);
      if (project == null) {
        throw 'Project not found';
      }

      if (!project.isManager(currentUser.uid)) {
        throw 'You do not have permission to reject join requests for this project';
      }

      // Update join request status
      await _firestore
          .collection(_joinRequestsCollection)
          .doc(requestId)
          .update({
        'status': JoinRequestStatus.rejected.name,
        'respondedAt': Timestamp.fromDate(DateTime.now()),
        'respondedBy': currentUser.uid,
      });

      if (Get.isLogEnable) {
        Get.log('Join request rejected: $requestId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error rejecting join request: $e');
      }
      rethrow;
    }
  }

  /// Cancel a join request (by the requester)
  Future<void> cancelJoinRequest(String requestId) async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      // Get the join request
      final requestDoc = await _firestore
          .collection(_joinRequestsCollection)
          .doc(requestId)
          .get();

      if (!requestDoc.exists) {
        throw 'Join request not found';
      }

      final joinRequest = JoinRequestModel.fromFirestore(requestDoc);

      // Check if current user is the requester
      if (joinRequest.userId != currentUser.uid) {
        throw 'You can only cancel your own join requests';
      }

      // Check if request is still pending
      if (joinRequest.status != JoinRequestStatus.pending) {
        throw 'Cannot cancel a request that has already been responded to';
      }

      // Delete the join request
      await _firestore
          .collection(_joinRequestsCollection)
          .doc(requestId)
          .delete();

      if (Get.isLogEnable) {
        Get.log('Join request cancelled: $requestId');
      }
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error cancelling join request: $e');
      }
      rethrow;
    }
  }

  /// Get real-time stream of project join requests
  Stream<List<JoinRequestModel>> getProjectJoinRequestsStream(String projectId) {
    return _firestore
        .collection(_joinRequestsCollection)
        .where('projectId', isEqualTo: projectId)
        .where('status', isEqualTo: 'pending')
        .snapshots()
        .map((snapshot) {
          final requests = snapshot.docs
              .map((doc) => JoinRequestModel.fromFirestore(doc))
              .toList();
          
          // Sort by request date (newest first)
          requests.sort((a, b) => b.requestedAt.compareTo(a.requestedAt));
          
          return requests;
        });
  }

  /// Check if user has a pending request for a project
  Future<JoinRequestModel?> _getPendingRequest(String projectId, String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_joinRequestsCollection)
          .where('projectId', isEqualTo: projectId)
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: 'pending')
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return JoinRequestModel.fromFirestore(querySnapshot.docs.first);
      }

      return null;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error checking pending request: $e');
      }
      return null;
    }
  }

  /// Get all public projects for discovery
  Future<List<ProjectModel>> getPublicProjects() async {
    try {
      final currentUser = _authService.userModel;
      if (currentUser == null) {
        throw 'User not authenticated';
      }

      final querySnapshot = await _firestore
          .collection('projects')
          .where('visibility', isEqualTo: 'public')
          .get();

      final projects = querySnapshot.docs
          .map((doc) => ProjectModel.fromFirestore(doc))
          .where((project) => !project.isMember(currentUser.uid)) // Exclude projects user is already in
          .toList();

      // Sort by creation date (newest first)
      projects.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (Get.isLogEnable) {
        Get.log('Fetched ${projects.length} public projects');
      }

      return projects;
    } catch (e) {
      if (Get.isLogEnable) {
        Get.log('Error fetching public projects: $e');
      }
      rethrow;
    }
  }
}
